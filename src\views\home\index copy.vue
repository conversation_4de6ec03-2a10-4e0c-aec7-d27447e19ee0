<template>
  <div class="home">
    <div class="title"><i class="icon"></i>您好，欢迎使用采购文件合规性审查</div>
    <div class="tip">
      <div  style="margin-top: -10px;">
        请先上传采购文件，
      </div>
      <div class="text-box">
        <span class="text">解锁审查新旅程</span>
      </div>
    </div>
    <div v-show="!showUploadModal" class="upload-wrapper">
      <upload-file
      ref="uploadFileRef"
      v-model:files="fileList"
      button-text="上传文档"
      accept=".pdf,.doc,.docx"
      accept-tip= "标书文件只能是pdf/word文件"
      :show-upload-list="false"
      @change="doChange">
        <div class="upload">
          <img width="200" height="200" alt="" src="@/assets/images/bid-examine/upload.png">
          <div class="t1">点击上传采购文件</div>
          <div class="t2">仅支持PDF/WORD格式文档，单个文档大小不超过<span class="strong">20</span>M，最多上传<span class="strong">10</span>份</div>
          <div class="t3">您上传的所有文件在您审查档案库中查看，其他人不可见</div>
        </div>
      </upload-file>
  </div>
    <uploader v-model:files="fileList" :check-visible="showUploadModal" @close="doClose"></uploader>
  </div>
</template>

<script setup lang='ts'>
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { CopyOutlined, SearchOutlined } from '@ant-design/icons-vue'
import Uploader from '@/views/home/<USER>/uploader.vue'
import UploadFile from '@/components/UploadFile/index.vue'
const router = useRouter()
const fileList = ref([])
const uploaderVisible = ref(false)
function doChange() {
  uploaderVisible.value = true
}
const showUploadModal = computed(() => fileList.value.length > 0 && uploaderVisible.value)
function doClose() {
  uploaderVisible.value = false
  fileList.value = []
}
</script>

<style lang="scss" scoped>
$color-add-text: #4E5969;
.home {
  max-width: unset;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 16px; 
  .title {
    display: flex;
    align-items: center;
    font-size: 34px;
    font-weight: 500;
    margin-top: 32px;
    .icon {
      display: inline-block;
      vertical-align: middle;
      margin: -3px 8px 0;
      width: 48px;
      height: 48px;
      background: url('@/assets/images/bid-examine/home-welcome.png') no-repeat;
      background-size: 100% auto;
    }
  }
  .tip {
    display: flex;
    align-items: center;
    font-size: 24px;
    font-weight: 500;
    margin: 4px 0 36px;
    .text-box {
      background: url('@/assets/images/bid-examine/home-text-bg.png') no-repeat center bottom;
      background-size: 100% auto;
      padding-bottom: 12px;
      .text {
        font-size: 24px;
        font-weight: 500;
        line-height: normal;
        text-align: center;
        letter-spacing: normal;
        background: linear-gradient(96deg, #165DFF 1%, #8142FF 99%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
  }
  .upload-wrapper {
    display: flex;
  }
  .upload {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--neutral-3);
    background-color: var(--neutral-1);
    width: 896px;
    height: 432px;
    border-radius: 12px;
    transition: all 0.3s;
    &:hover {
      transform: translateY(-10px);
    }
    .t1 {
      font-size: 26px;
      font-weight: 500;
      margin-top: 24px;
    }

    .t2 {
      font-size: 16px;
      color: $color-add-text;
      .strong {
        font-weight: bold;
        color: var(--main-6);
        padding: 0 4px;
      }
    }

    .t3 {
      font-size: 12px;
      color: var(--text-3);
      padding: 4px 16px;
      border-radius: 20px;
      margin-top: 12px;
      overflow: hidden;
      box-sizing: border-box;
      background: var(--fill-0);
      box-sizing: border-box;
      border: 1px solid var(--fill-0);
    }
  }
  :deep(.uploader .body) {
    width: 896px;
  }
}
</style>
