import { Clock,FileText, CheckCircle2, <PERSON><PERSON><PERSON><PERSON>gle, CircleX,Loader } from 'lucide-vue-next'
/**
 * 审查状态<<CircleX /> />
 */
export enum ReviewStatus {
  ERROR = -1, NONE, DOING, DONE
}

export const statusOptions = [
  {label: '待审查', value: 0},
  {label: '审查中', value: 1, color: 'orange'},
  {label: '审查完成', value: 2, color: 'green'},
  {label: '审查失败', value: -1, color: 'red'},
]

/**
 * 风险选项
 */
export const riskOptions = [
  {label: '未发现风险', value: 0, color: 'default'},
  {label: '发现风险', value: 1, color: 'red'}
]
// 审核状态
export const getStatusStyle = (status: number) => {
  const statusConfig = {
    0: { // 待审查
      style: {
        backgroundColor: 'rgba(107, 114, 128, 0.1)',
        borderColor: 'rgba(107, 114, 128, 0.2)',
        color: '#6B7280'
      },
      text: '待审查',
      icon: Loader

    },
    1: { // 审查中
      style: {
        backgroundColor: 'rgba(19, 60, 232, 0.1)',
        borderColor: 'rgba(19, 60, 232, 0.2)',
        color: '#133CE8'
      },
      text: '审查中',
      icon: FileText
    },
    2: { // 审查完成
      style: {
        backgroundColor: 'rgba(78, 171, 12, 0.1)',
        borderColor: 'rgba(78, 171, 12, 0.2)',
        color: '#4EAB0C'
      },
      text: '审查完成',
      icon: CheckCircle2
    },
    '-1': { // 审查失败
      style: {
        backgroundColor: 'rgba(239, 68, 68, 0.1)',
        borderColor: 'rgba(239, 68, 68, 0.2)',
        color: '#EF4444'
      },
      text: '审查失败',
      icon: CircleX
    }
  }
  return statusConfig[status as keyof typeof statusConfig] || statusConfig[0]
}