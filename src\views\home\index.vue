<template>
  <div class="home">
    <!-- 主容器卡片 -->
    <div class="main-card">
      <!-- 标题区域 -->
      <div class="header-section">
        <div class="title-row">
          <img class="welcome-icon" src="@/assets/images/bid-examine/home-welcome.png" alt="欢迎图标">
          <span class="title-text">您好，欢迎使用采购文件合规性审查</span>
        </div>
      </div>

      <!-- 上传区域 -->
      <div v-show="!showUploadModal" class="upload-section">
        <upload-file
          ref="uploadFileRef"
          v-model:files="fileList"
          button-text="上传文档"
          accept=".docx"
          accept-tip="文件格式不正确，请选择 .docx 文件"
          :show-upload-list="false"
          @change="doChange">
          <div class="upload-area">
            <!-- 上传图标 -->
            <div class="upload-icon-container">
              <div class="upload-icon">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                  <path d="M16 4L9.33 10.67L16 4Z" stroke="#2563EB" stroke-width="2.67" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M16 4V20" stroke="#2563EB" stroke-width="2.67" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M4 20V24C4 25.33 5.07 26.4 6.4 26.4H25.6C26.93 26.4 28 25.33 28 24V20" stroke="#2563EB" stroke-width="2.67" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
            <!-- 上传文字 -->
            <div class="upload-text-main">上传采购文件</div>
            <div class="upload-text-sub">仅支持 .docx 格式文档，单个文档大小不超过 20MB</div>
            <div class="upload-text-hint">或将文件拖拽到此处</div>
          </div>
        </upload-file>
      </div>

      <!-- 按钮区域 -->
      <div class="button-section">
        <button class="start-button" :class="{ disabled: fileList.length === 0 }" @click="handleStartReview">
          开始审查
        </button>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="showErrorMessage" class="message-container">
      <div class="message-content">
        <svg class="message-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path d="M8 1C4.13 1 1 4.13 1 8C1 11.87 4.13 15 8 15C11.87 15 15 11.87 15 8C15 4.13 11.87 1 8 1ZM8.5 11.5H7.5V10.5H8.5V11.5ZM8.5 9.5H7.5V4.5H8.5V9.5Z" fill="#133CE8"/>
        </svg>
        <span class="message-text">文件格式不正确，请选择 .docx 文件</span>
      </div>
    </div>

    <uploader v-model:files="fileList" :check-visible="showUploadModal" @close="doClose"></uploader>
  </div>
</template>

<script setup lang='ts'>
import { computed, ref } from 'vue'
import Uploader from '@/views/home/<USER>/uploader.vue'
import UploadFile from '@/components/UploadFile/index.vue'

const fileList = ref([])
const uploaderVisible = ref(false)
const showErrorMessage = ref(false)

function doChange() {
  uploaderVisible.value = true
}

const showUploadModal = computed(() => fileList.value.length > 0 && uploaderVisible.value)

function doClose() {
  uploaderVisible.value = false
  fileList.value = []
}

function handleStartReview() {
  if (fileList.value.length === 0) {
    showErrorMessage.value = true
    setTimeout(() => {
      showErrorMessage.value = false
    }, 3000)
    return
  }
  // 这里可以添加开始审查的逻辑
  console.log('开始审查')
}
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
  background-color: #F9FAFB;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 40px 192px;
  box-sizing: border-box;

  .main-card {
    width: 1280px;
    background: #FFFFFF;
    border-radius: 16px;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    padding: 32px;
    display: flex;
    flex-direction: column;
    gap: 32px;
  }

  .header-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;

    .title-row {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 0 357px;

      .welcome-icon {
        width: 48px;
        height: 48px;
        flex-shrink: 0;
      }

      .title-text {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 600;
        font-size: 30px;
        line-height: 1.2em;
        color: #111827;
        text-align: center;
      }
    }
  }

  .upload-section {
    display: flex;
    justify-content: center;
  }

  .upload-area {
    width: 100%;
    height: 246px;
    background: #F7F8FA;
    border: 1px solid #E5E6EB;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: #2563EB;
      border-width: 2px;
    }

    .upload-icon-container {
      margin-bottom: 16px;

      .upload-icon {
        width: 64px;
        height: 64px;
        background: #FFFFFF;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .upload-text-main {
      font-family: 'PingFang SC', sans-serif;
      font-weight: 400;
      font-size: 20px;
      line-height: 1.4em;
      color: #111827;
      text-align: center;
      margin-bottom: 12px;
    }

    .upload-text-sub {
      font-family: 'PingFang SC', sans-serif;
      font-weight: 400;
      font-size: 16px;
      line-height: 1.5em;
      color: #4B5563;
      text-align: center;
      margin-bottom: 8px;
    }

    .upload-text-hint {
      font-family: 'PingFang SC', sans-serif;
      font-weight: 400;
      font-size: 14px;
      line-height: 1.4285714285714286em;
      color: #6B7280;
      text-align: center;
    }
  }

  .button-section {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .start-button {
      width: 138px;
      height: 48px;
      background: #D1D5DB;
      border-radius: 6px;
      border: none;
      font-family: 'Inter', sans-serif;
      font-weight: 600;
      font-size: 16px;
      line-height: 1.5em;
      color: #6B7280;
      cursor: not-allowed;
      transition: all 0.2s ease;

      &:not(.disabled) {
        background: var(--main-6);
        color: #FFFFFF;
        cursor: pointer;

        &:hover {
          background: var(--main-5);
        }

        &:active {
          background: var(--main-7);
        }
      }
    }
  }

  .message-container {
    position: fixed;
    top: 24px;
    right: 50%;
    transform: translateX(50%);
    z-index: 1000;

    .message-content {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: #FFFFFF;
      border-radius: 6px;
      box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
                  0px 3px 6px -4px rgba(0, 0, 0, 0.12),
                  0px 6px 16px 0px rgba(0, 0, 0, 0.08);

      .message-icon {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
      }

      .message-text {
        font-family: 'Inter', sans-serif;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.5714285714285714em;
        color: rgba(0, 0, 0, 0.88);
      }
    }
  }

  :deep(.uploader .body) {
    width: 1280px;
  }
}
</style>
