<template>
  <div class="home">
    <!-- 主容器卡片 -->
    <div class="main-card">
      <!-- 标题区域 -->
      <div class="header-section">
        <div class="title-row">
          <img class="welcome-icon" src="@/assets/images/bid-examine/home-welcome.png" alt="欢迎图标">
          <span class="title-text">您好，欢迎使用采购文件合规性审查</span>
        </div>
      </div>

      <!-- 上传区域 -->
      <div v-show="fileList.length === 0" class="upload-section">
        <upload-file
          ref="uploadFileRef"
          v-model:files="fileList"
          button-text="上传文档"
          accept=".docx"
          accept-tip="文件格式不正确，请选择 .docx 文件"
          :show-upload-list="false"
          @change="doChange">
          <div class="upload-area">
            <!-- 上传图标 -->
            <div class="upload-icon-container">
              <div class="upload-icon">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                  <path d="M16 4L9.33 10.67L16 4Z" stroke="#2563EB" stroke-width="2.67" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M16 4V20" stroke="#2563EB" stroke-width="2.67" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M4 20V24C4 25.33 5.07 26.4 6.4 26.4H25.6C26.93 26.4 28 25.33 28 24V20" stroke="#2563EB" stroke-width="2.67" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </div>
            </div>
            <!-- 上传文字 -->
            <div class="upload-text-main">上传采购文件</div>
            <div class="upload-text-sub">仅支持 .docx 格式文档，单个文档大小不超过 20MB</div>
            <div class="upload-text-hint">或将文件拖拽到此处</div>
          </div>
        </upload-file>
      </div>

      <!-- 文件列表区域 -->
      <div v-show="fileList.length > 0" class="file-list-section">
        <!-- 文件列表标题和按钮 -->
        <div class="file-list-header">
          <div class="file-count-text">已上传文件 ({{ fileList.length }}/{{ Math.max(1, fileList.length) }})</div>
          <div class="header-actions">
            <!-- 测试按钮 -->
            <button class="test-button" @click="addTestFiles">测试状态</button>
            <button class="start-button" :class="{ disabled: !canStartReview }" @click="handleStartReview">
              开始审查
            </button>
          </div>
        </div>

        <!-- 文件项列表 -->
        <div class="file-items-container">
          <div v-for="file in fileList" :key="file.uid" class="file-item" :class="getFileItemClass(file)">
            <!-- 删除按钮 -->
            <div class="file-close-btn" @click="handleRemoveFile(file)">
              <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
                <path d="M1 1L7 7M7 1L1 7" stroke="currentColor" stroke-width="1.4" stroke-linecap="round"/>
              </svg>
            </div>

            <!-- 文件图标 -->
            <div class="file-icon-container">
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                <!-- DOCX文件图标 -->
                <rect width="23" height="28" x="4" y="2" fill="#3B66F5" rx="2"/>
                <rect width="7.11" height="7.11" x="16" y="0" fill="rgba(255, 255, 255, 0.7)" rx="1"/>
                <rect width="11.67" height="8.98" x="6" y="12" fill="#FFFFFF" rx="1"/>
              </svg>
            </div>

            <!-- 文件信息 -->
            <div class="file-info">
              <div class="file-name" :title="file.name">{{ file.name }}</div>
              <div class="file-size">{{ formatFileSize(file.size) }}</div>
            </div>

            <!-- 状态区域 -->
            <div class="file-status">
              <!-- 上传成功状态 -->
              <div v-if="file.status === 'done'" class="status-success">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path d="M13.5 4.5L6 12L2.5 8.5" stroke="#52C41A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="status-text">上传成功</span>
              </div>

              <!-- 上传失败状态 -->
              <div v-else-if="file.status === 'error'" class="status-error">
                <div class="error-info">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <circle cx="8" cy="8" r="7" stroke="#F5222D" stroke-width="2"/>
                    <path d="M8 4V8M8 12H8.01" stroke="#F5222D" stroke-width="2" stroke-linecap="round"/>
                  </svg>
                  <span class="status-text">上传失败</span>
                </div>
                <button class="retry-btn" @click="handleRetryUpload(file)">重试</button>
              </div>

              <!-- 上传中状态 -->
              <div v-else-if="file.status === 'uploading'" class="status-uploading">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" class="loading-icon">
                  <circle cx="8" cy="8" r="6" stroke="#133CE8" stroke-width="2" stroke-dasharray="37.7" stroke-dashoffset="37.7">
                    <animate attributeName="stroke-dashoffset" dur="1s" values="37.7;0" repeatCount="indefinite"/>
                  </circle>
                </svg>
                <span class="status-text">上传中</span>
              </div>
            </div>

            <!-- 进度条 -->
            <div v-if="file.status === 'uploading'" class="progress-bar">
              <div class="progress-fill" :style="{ width: `${file.percent || 0}%` }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="showErrorMessage" class="message-container">
      <div class="message-content">
        <svg class="message-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
          <path d="M8 1C4.13 1 1 4.13 1 8C1 11.87 4.13 15 8 15C11.87 15 15 11.87 15 8C15 4.13 11.87 1 8 1ZM8.5 11.5H7.5V10.5H8.5V11.5ZM8.5 9.5H7.5V4.5H8.5V9.5Z" fill="#133CE8"/>
        </svg>
        <span class="message-text">文件格式不正确，请选择 .docx 文件</span>
      </div>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { computed, ref } from 'vue'
import UploadFile from '@/components/UploadFile/index.vue'

interface FileItem {
  uid: string
  name: string
  size: number
  status: 'uploading' | 'done' | 'error'
  percent?: number
  response?: any
}

const fileList = ref<FileItem[]>([])
const showErrorMessage = ref(false)
const uploadFileRef = ref()

function doChange() {
  // 文件选择后的处理逻辑
  // 添加测试数据来展示不同状态
  if (fileList.value.length === 0) {
    // 添加一个上传成功的文件示例
    fileList.value.push({
      uid: 'test-1',
      name: 'SZCG2025000310-A深圳大学多光子成像显微镜采购[1].docx',
      size: 878592, // 858.02 KB
      status: 'done',
      response: { fileId: 'test-file-1' }
    })
  }
}

// 计算是否可以开始审查
const canStartReview = computed(() => {
  return fileList.value.length > 0 && fileList.value.every(file => file.status === 'done')
})

// 获取文件项的CSS类
function getFileItemClass(file: FileItem) {
  switch (file.status) {
    case 'done':
      return 'file-item-success'
    case 'error':
      return 'file-item-error'
    case 'uploading':
      return 'file-item-uploading'
    default:
      return ''
  }
}

// 格式化文件大小
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 删除文件
function handleRemoveFile(file: FileItem) {
  uploadFileRef.value?.handleRemove(file)
}

// 重试上传
function handleRetryUpload(file: FileItem) {
  // 重试上传逻辑
  file.status = 'uploading'
  file.percent = 0
  // 这里可以添加重新上传的逻辑
}

// 添加测试文件（用于展示不同状态）
function addTestFiles() {
  fileList.value = [
    {
      uid: 'test-success',
      name: 'SZCG2025000310-A深圳大学多光子成像显微镜采购[1].docx',
      size: 878592, // 858.02 KB
      status: 'done',
      response: { fileId: 'test-file-success' }
    },
    {
      uid: 'test-error',
      name: '采购文件示例-上传失败.docx',
      size: 1024000, // 1MB
      status: 'error'
    },
    {
      uid: 'test-uploading',
      name: '采购文件示例-上传中.docx',
      size: 2048000, // 2MB
      status: 'uploading',
      percent: 65
    }
  ]
}

function handleStartReview() {
  if (!canStartReview.value) {
    showErrorMessage.value = true
    setTimeout(() => {
      showErrorMessage.value = false
    }, 3000)
    return
  }
  // 这里可以添加开始审查的逻辑
  console.log('开始审查')
}
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
  background-color: #F9FAFB;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 40px 192px;
  box-sizing: border-box;

  .main-card {
    width: 1280px;
    background: #FFFFFF;
    border-radius: 16px;
    box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    padding: 32px;
    display: flex;
    flex-direction: column;
    gap: 32px;
  }

  .header-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;

    .title-row {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 0 357px;

      .welcome-icon {
        width: 48px;
        height: 48px;
        flex-shrink: 0;
      }

      .title-text {
        font-family: 'PingFang SC', sans-serif;
        font-weight: 600;
        font-size: 30px;
        line-height: 1.2em;
        color: #111827;
        text-align: center;
      }
    }
  }

  .upload-section {
    display: flex;
    justify-content: center;
  }

  .upload-area {
    width: 100%;
    height: 246px;
    background: #F7F8FA;
    border: 1px solid #E5E6EB;
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      border-color: #2563EB;
      border-width: 2px;
    }

    .upload-icon-container {
      margin-bottom: 16px;

      .upload-icon {
        width: 64px;
        height: 64px;
        background: #FFFFFF;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .upload-text-main {
      font-family: 'PingFang SC', sans-serif;
      font-weight: 400;
      font-size: 20px;
      line-height: 1.4em;
      color: #111827;
      text-align: center;
      margin-bottom: 12px;
    }

    .upload-text-sub {
      font-family: 'PingFang SC', sans-serif;
      font-weight: 400;
      font-size: 16px;
      line-height: 1.5em;
      color: #4B5563;
      text-align: center;
      margin-bottom: 8px;
    }

    .upload-text-hint {
      font-family: 'PingFang SC', sans-serif;
      font-weight: 400;
      font-size: 14px;
      line-height: 1.4285714285714286em;
      color: #6B7280;
      text-align: center;
    }
  }

  // 文件列表区域
  .file-list-section {
    display: flex;
    flex-direction: column;
    gap: 24px;

    .file-list-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .file-count-text {
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        font-size: 20px;
        line-height: 1.4em;
        color: #000000;
      }

      .header-actions {
        display: flex;
        align-items: center;
        gap: 16px;

        .test-button {
          padding: 8px 16px;
          height: 32px;
          background: #F0F5FF;
          border: 1px solid #8FB0FF;
          border-radius: 4px;
          font-family: 'Inter', sans-serif;
          font-weight: 400;
          font-size: 14px;
          color: #133CE8;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #E6EFFF;
            border-color: #3B66F5;
          }
        }

        .start-button {
          width: 138px;
          height: 48px;
          background: #133CE8;
          border-radius: 6px;
          border: none;
          font-family: 'Inter', sans-serif;
          font-weight: 600;
          font-size: 16px;
          line-height: 1.5em;
          color: #FFFFFF;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #3B66F5;
          }

          &:active {
            background: #0625C2;
          }

          &.disabled {
            background: #D1D5DB;
            color: #6B7280;
            cursor: not-allowed;
          }
        }
      }
    }

    .file-items-container {
      display: flex;
      flex-direction: row;
      gap: 16px;
      flex-wrap: wrap;
    }

    .file-item {
      position: relative;
      width: 394px;
      padding: 16px;
      border-radius: 8px;
      background: #FFFFFF;
      border: 1px solid #E5E6EB;
      display: flex;
      align-items: center;
      gap: 12px;
      transition: all 0.2s ease;

      &.file-item-success {
        background: #F6FFED;
        border-color: #B7EB8F;
      }

      &.file-item-error {
        background: #FFF1F0;
        border-color: #FFA39E;
      }

      &.file-item-uploading {
        background: #F0F5FF;
        border-color: #8FB0FF;
      }

      .file-close-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 20px;
        height: 20px;
        background: #FFFFFF;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
                    0px 3px 6px -4px rgba(0, 0, 0, 0.12),
                    0px 6px 16px 0px rgba(0, 0, 0, 0.08);
        color: #000000;
        transition: all 0.2s ease;

        &:hover {
          color: #F5222D;
        }
      }

      .file-icon-container {
        flex-shrink: 0;
      }

      .file-info {
        flex: 1;
        min-width: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;

        .file-name {
          font-family: 'Inter', sans-serif;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.5714285714285714em;
          color: rgba(0, 0, 0, 0.88);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 4px;
        }

        .file-size {
          font-family: 'Inter', sans-serif;
          font-weight: 400;
          font-size: 14px;
          line-height: 1.5714285714285714em;
          color: rgba(0, 0, 0, 0.45);
        }
      }

      .file-status {
        flex-shrink: 0;
        display: flex;
        align-items: center;
        gap: 4px;

        .status-success,
        .status-uploading {
          display: flex;
          align-items: center;
          gap: 4px;

          .status-text {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.5714285714285714em;
          }
        }

        .status-success {
          .status-text {
            color: #52C41A;
          }
        }

        .status-uploading {
          .status-text {
            color: #133CE8;
          }

          .loading-icon {
            animation: spin 1s linear infinite;
          }
        }

        .status-error {
          display: flex;
          align-items: center;
          gap: 8px;

          .error-info {
            display: flex;
            align-items: center;
            gap: 4px;

            .status-text {
              font-family: 'Inter', sans-serif;
              font-weight: 400;
              font-size: 14px;
              line-height: 1.5714285714285714em;
              color: #F5222D;
            }
          }

          .retry-btn {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.5714285714285714em;
            color: #133CE8;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            text-decoration: underline;

            &:hover {
              color: #3B66F5;
            }
          }
        }
      }

      .progress-bar {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: #FFFFFF;
        border-radius: 0 0 8px 8px;
        overflow: hidden;

        .progress-fill {
          height: 100%;
          background: #133CE8;
          border-radius: 0 0 8px 8px;
          transition: width 0.3s ease;
        }
      }
    }
  }

  .message-container {
    position: fixed;
    top: 24px;
    right: 50%;
    transform: translateX(50%);
    z-index: 1000;

    .message-content {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: #FFFFFF;
      border-radius: 6px;
      box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
                  0px 3px 6px -4px rgba(0, 0, 0, 0.12),
                  0px 6px 16px 0px rgba(0, 0, 0, 0.08);

      .message-icon {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
      }

      .message-text {
        font-family: 'Inter', sans-serif;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.5714285714285714em;
        color: rgba(0, 0, 0, 0.88);
      }
    }
  }

  // 动画
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  :deep(.uploader .body) {
    width: 1280px;
  }
}
</style>
