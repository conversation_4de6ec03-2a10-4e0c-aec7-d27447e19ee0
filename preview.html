<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>合规性审查页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Inter', sans-serif;
            background-color: #F9FAFB;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 40px 192px;
        }

        .main-card {
            width: 1280px;
            background: #FFFFFF;
            border-radius: 16px;
            box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
            padding: 32px;
            display: flex;
            flex-direction: column;
            gap: 32px;
        }

        .header-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .title-row {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 0 357px;
        }

        .welcome-icon {
            width: 48px;
            height: 48px;
            flex-shrink: 0;
            background: #E6EFFF;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .title-text {
            font-family: 'PingFang SC', sans-serif;
            font-weight: 600;
            font-size: 30px;
            line-height: 1.2em;
            color: #111827;
            text-align: center;
        }

        .file-list-section {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .file-list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-count-text {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 20px;
            line-height: 1.4em;
            color: #000000;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .test-button {
            padding: 8px 16px;
            height: 32px;
            background: #F0F5FF;
            border: 1px solid #8FB0FF;
            border-radius: 4px;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14px;
            color: #133CE8;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .test-button:hover {
            background: #E6EFFF;
            border-color: #3B66F5;
        }

        .start-button {
            width: 138px;
            height: 48px;
            background: #133CE8;
            border-radius: 6px;
            border: none;
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 16px;
            line-height: 1.5em;
            color: #FFFFFF;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .start-button:hover {
            background: #3B66F5;
        }

        .start-button.disabled {
            background: #D1D5DB;
            color: #6B7280;
            cursor: not-allowed;
        }

        .file-items-container {
            display: flex;
            flex-direction: row;
            gap: 16px;
            flex-wrap: wrap;
        }

        .file-item {
            position: relative;
            width: 394px;
            padding: 16px;
            border-radius: 8px;
            background: #FFFFFF;
            border: 1px solid #E5E6EB;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.2s ease;
        }

        .file-item-success {
            background: #F6FFED;
            border-color: #B7EB8F;
        }

        .file-item-error {
            background: #FFF1F0;
            border-color: #FFA39E;
        }

        .file-item-uploading {
            background: #F0F5FF;
            border-color: #8FB0FF;
        }

        .file-close-btn {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 20px;
            height: 20px;
            background: #FFFFFF;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),
                        0px 3px 6px -4px rgba(0, 0, 0, 0.12),
                        0px 6px 16px 0px rgba(0, 0, 0, 0.08);
            color: #000000;
            transition: all 0.2s ease;
        }

        .file-close-btn:hover {
            color: #F5222D;
        }

        .file-icon-container {
            flex-shrink: 0;
        }

        .file-info {
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .file-name {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.5714285714285714em;
            color: rgba(0, 0, 0, 0.88);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 4px;
        }

        .file-size {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.5714285714285714em;
            color: rgba(0, 0, 0, 0.45);
        }

        .file-status {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-success,
        .status-uploading {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-text {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.5714285714285714em;
        }

        .status-success .status-text {
            color: #52C41A;
        }

        .status-uploading .status-text {
            color: #133CE8;
        }

        .status-error {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .error-info {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .status-error .status-text {
            color: #F5222D;
        }

        .retry-btn {
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.5714285714285714em;
            color: #133CE8;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            text-decoration: underline;
        }

        .retry-btn:hover {
            color: #3B66F5;
        }

        .progress-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: #FFFFFF;
            border-radius: 0 0 8px 8px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #133CE8;
            border-radius: 0 0 8px 8px;
            transition: width 0.3s ease;
            width: 65%;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .loading-icon {
            animation: spin 1s linear infinite;
        }
    </style>
</head>
<body>
    <div class="main-card">
        <!-- 标题区域 -->
        <div class="header-section">
            <div class="title-row">
                <div class="welcome-icon">📋</div>
                <span class="title-text">您好，欢迎使用采购文件合规性审查</span>
            </div>
        </div>

        <!-- 文件列表区域 -->
        <div class="file-list-section">
            <!-- 文件列表标题和按钮 -->
            <div class="file-list-header">
                <div class="file-count-text">已上传文件 (3/3)</div>
                <div class="header-actions">
                    <button class="test-button">测试状态</button>
                    <button class="start-button disabled">开始审查</button>
                </div>
            </div>

            <!-- 文件项列表 -->
            <div class="file-items-container">
                <!-- 上传成功的文件 -->
                <div class="file-item file-item-success">
                    <div class="file-close-btn">
                        <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
                            <path d="M1 1L7 7M7 1L1 7" stroke="currentColor" stroke-width="1.4" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <div class="file-icon-container">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                            <rect width="23" height="28" x="4" y="2" fill="#3B66F5" rx="2"/>
                            <rect width="7.11" height="7.11" x="16" y="0" fill="rgba(255, 255, 255, 0.7)" rx="1"/>
                            <rect width="11.67" height="8.98" x="6" y="12" fill="#FFFFFF" rx="1"/>
                        </svg>
                    </div>
                    <div class="file-info">
                        <div class="file-name" title="SZCG2025000310-A深圳大学多光子成像显微镜采购[1].docx">SZCG2025000310-A深圳大学多光子成像显微镜采购[1].docx</div>
                        <div class="file-size">858.02 KB</div>
                    </div>
                    <div class="file-status">
                        <div class="status-success">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M13.5 4.5L6 12L2.5 8.5" stroke="#52C41A" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span class="status-text">上传成功</span>
                        </div>
                    </div>
                </div>

                <!-- 上传失败的文件 -->
                <div class="file-item file-item-error">
                    <div class="file-close-btn">
                        <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
                            <path d="M1 1L7 7M7 1L1 7" stroke="currentColor" stroke-width="1.4" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <div class="file-icon-container">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                            <rect width="23" height="28" x="4" y="2" fill="#3B66F5" rx="2"/>
                            <rect width="7.11" height="7.11" x="16" y="0" fill="rgba(255, 255, 255, 0.7)" rx="1"/>
                            <rect width="11.67" height="8.98" x="6" y="12" fill="#FFFFFF" rx="1"/>
                        </svg>
                    </div>
                    <div class="file-info">
                        <div class="file-name">采购文件示例-上传失败.docx</div>
                        <div class="file-size">1.00 MB</div>
                    </div>
                    <div class="file-status">
                        <div class="status-error">
                            <div class="error-info">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                    <circle cx="8" cy="8" r="7" stroke="#F5222D" stroke-width="2"/>
                                    <path d="M8 4V8M8 12H8.01" stroke="#F5222D" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                                <span class="status-text">上传失败</span>
                            </div>
                            <button class="retry-btn">重试</button>
                        </div>
                    </div>
                </div>

                <!-- 上传中的文件 -->
                <div class="file-item file-item-uploading">
                    <div class="file-close-btn">
                        <svg width="8" height="8" viewBox="0 0 8 8" fill="none">
                            <path d="M1 1L7 7M7 1L1 7" stroke="currentColor" stroke-width="1.4" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <div class="file-icon-container">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
                            <rect width="23" height="28" x="4" y="2" fill="#3B66F5" rx="2"/>
                            <rect width="7.11" height="7.11" x="16" y="0" fill="rgba(255, 255, 255, 0.7)" rx="1"/>
                            <rect width="11.67" height="8.98" x="6" y="12" fill="#FFFFFF" rx="1"/>
                        </svg>
                    </div>
                    <div class="file-info">
                        <div class="file-name">采购文件示例-上传中.docx</div>
                        <div class="file-size">2.00 MB</div>
                    </div>
                    <div class="file-status">
                        <div class="status-uploading">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" class="loading-icon">
                                <circle cx="8" cy="8" r="6" stroke="#133CE8" stroke-width="2" stroke-dasharray="37.7" stroke-dashoffset="37.7">
                                    <animate attributeName="stroke-dashoffset" dur="1s" values="37.7;0" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                            <span class="status-text">上传中</span>
                        </div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
